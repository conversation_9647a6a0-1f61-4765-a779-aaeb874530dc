{"name": "食物营养分析工作流_Gemini_修复版", "nodes": [{"parameters": {"httpMethod": "POST", "path": "food-analysis-gemini", "responseMode": "responseNode", "options": {"rawBody": true}}, "id": "webhook-receiver", "name": "接收图片上传", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "food-analysis-gemini-webhook"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "check-image", "leftValue": "={{ $json.image }}", "rightValue": "", "operator": {"type": "string", "operation": "isNotEmpty"}}], "combinator": "and"}, "options": {}}, "id": "validate-input", "name": "验证输入", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"jsCode": "// 解码base64图片数据并准备Gemini请求\nconst imageData = $input.first().json.image;\n\n// 验证图片数据格式\nif (!imageData || typeof imageData !== 'string') {\n  throw new Error('无效的图片数据格式');\n}\n\n// 将图片转换为base64字符串（Gemini需要）\nconst base64Image = imageData;\n\n// 添加调试信息\nconsole.log('图片数据长度:', base64Image.length);\nconsole.log('图片数据前50字符:', base64Image.substring(0, 50));\n\n// 验证base64格式\nif (!base64Image.startsWith('data:image/') && !/^[A-Za-z0-9+/]*={0,2}$/.test(base64Image)) {\n  throw new Error('无效的base64图片格式');\n}\n\nreturn [{\n  json: {\n    base64Image: base64Image,\n    originalData: $input.first().json,\n    imageSize: base64Image.length,\n    timestamp: new Date().toISOString(),\n    imageFormat: base64Image.startsWith('data:image/') ? \n      base64Image.split(';')[0].split(':')[1] : 'unknown'\n  }\n}];"}, "id": "decode-image", "name": "解码图片", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"resource": "gemini", "operation": "generateContent", "model": "gemini-1.5-pro", "prompt": "请分析这张图片中的食物，并提供详细的营养分析。请识别图片中的所有食物，并分析其营养成分。\n\n请以JSON格式返回结果，包含以下字段：\n{\n  \"foodName\": \"食物名称\",\n  \"calories\": 卡路里数值,\n  \"protein\": 蛋白质克数,\n  \"carbs\": 碳水化合物克数,\n  \"fat\": 脂肪克数,\n  \"fiber\": 纤维克数,\n  \"vitamins\": \"维生素含量描述\",\n  \"minerals\": \"矿物质含量描述\",\n  \"healthTips\": \"健康建议\",\n  \"detectedFoods\": [\"检测到的食物列表\"]\n}\n\n请确保返回的是有效的JSON格式，不要包含其他文本。", "options": {"temperature": 0.3, "maxOutputTokens": 1000, "topP": 0.8, "topK": 40}, "binaryData": "={{ $json.base64Image }}"}, "id": "gemini-analysis", "name": "Gemini智能分析", "type": "n8n-nodes-base.googleGemini", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"jsCode": "// 解析Gemini分析结果\nconst geminiResult = $input.first().json;\nlet nutritionData;\nlet errorMessage = null;\n\n// 添加调试信息\nconsole.log('Gemini响应结构:', Object.keys(geminiResult));\nconsole.log('Gemini响应类型:', typeof geminiResult);\n\n// 检查响应结构\nif (!geminiResult.candidates || !geminiResult.candidates[0]) {\n  errorMessage = 'Gemini响应格式错误';\n  nutritionData = createDefaultNutritionData();\n} else {\n  try {\n    // 尝试解析Gemini返回的JSON\n    const responseText = geminiResult.candidates[0].content.parts[0].text;\n    console.log('Gemini原始响应:', responseText);\n    \n    // 提取JSON部分（Gemini可能返回额外的文本）\n    const jsonMatch = responseText.match(/\\{[\\s\\S]*\\}/);\n    if (jsonMatch) {\n      try {\n        nutritionData = JSON.parse(jsonMatch[0]);\n        console.log('解析成功:', nutritionData);\n      } catch (parseError) {\n        console.error('JSON解析错误:', parseError);\n        errorMessage = 'JSON解析失败: ' + parseError.message;\n        nutritionData = createDefaultNutritionData();\n      }\n    } else {\n      console.log('未找到JSON格式响应');\n      errorMessage = '未找到有效的JSON响应';\n      nutritionData = createDefaultNutritionData();\n    }\n  } catch (error) {\n    console.error('处理Gemini响应时出错:', error);\n    errorMessage = '处理响应时出错: ' + error.message;\n    nutritionData = createDefaultNutritionData();\n  }\n}\n\n// 创建默认营养数据函数\nfunction createDefaultNutritionData() {\n  return {\n    foodName: '未知食物',\n    calories: 0,\n    protein: 0,\n    carbs: 0,\n    fat: 0,\n    fiber: 0,\n    vitamins: '未检测到',\n    minerals: '未检测到',\n    healthTips: '请上传更清晰的食物图片',\n    detectedFoods: []\n  };\n}\n\n// 验证营养数据完整性\nif (!nutritionData.foodName) nutritionData.foodName = '未知食物';\nif (!nutritionData.calories) nutritionData.calories = 0;\nif (!nutritionData.protein) nutritionData.protein = 0;\nif (!nutritionData.carbs) nutritionData.carbs = 0;\nif (!nutritionData.fat) nutritionData.fat = 0;\nif (!nutritionData.fiber) nutritionData.fiber = 0;\nif (!nutritionData.vitamins) nutritionData.vitamins = '未检测到';\nif (!nutritionData.minerals) nutritionData.minerals = '未检测到';\nif (!nutritionData.healthTips) nutritionData.healthTips = '请咨询专业营养师';\nif (!nutritionData.detectedFoods) nutritionData.detectedFoods = [];\n\n// 格式化结果用于网页显示\nconst formattedResult = {\n  success: true,\n  foodName: nutritionData.foodName,\n  nutrition: {\n    calories: nutritionData.calories,\n    protein: nutritionData.protein,\n    carbs: nutritionData.carbs,\n    fat: nutritionData.fat,\n    fiber: nutritionData.fiber\n  },\n  vitamins: nutritionData.vitamins,\n  minerals: nutritionData.minerals,\n  healthTips: nutritionData.healthTips,\n  detectedFoods: nutritionData.detectedFoods,\n  analysisTime: new Date().toISOString(),\n  errorMessage: errorMessage,\n  rawResponse: geminiResult.candidates ? geminiResult.candidates[0].content.parts[0].text : '无响应'\n};\n\nreturn [{\n  json: formattedResult\n}];"}, "id": "format-results", "name": "格式化结果", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ $json }}", "options": {"responseCode": 200, "responseHeaders": {"parameters": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "POST, GET, OPTIONS"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type"}, {"name": "Cache-Control", "value": "no-cache"}]}}}, "id": "success-response", "name": "成功响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"请上传有效的食物图片\",\n  \"message\": \"图片数据无效或为空\",\n  \"timestamp\": \"{{ $now }}\"\n}", "options": {"responseCode": 400, "responseHeaders": {"parameters": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "POST, GET, OPTIONS"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type"}, {"name": "Cache-Control", "value": "no-cache"}]}}}, "id": "error-response", "name": "错误响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 500]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"图片分析失败\",\n  \"message\": \"无法识别图片中的食物，请上传更清晰的食物图片\",\n  \"timestamp\": \"{{ $now }}\"\n}", "options": {"responseCode": 500, "responseHeaders": {"parameters": [{"name": "Access-Control-Allow-Origin", "value": "*"}, {"name": "Access-Control-Allow-Methods", "value": "POST, GET, OPTIONS"}, {"name": "Access-Control-Allow-Headers", "value": "Content-Type"}, {"name": "Cache-Control", "value": "no-cache"}]}}}, "id": "analysis-error-response", "name": "分析错误响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 500]}], "connections": {"接收图片上传": {"main": [[{"node": "验证输入", "type": "main", "index": 0}]]}, "验证输入": {"main": [[{"node": "解码图片", "type": "main", "index": 0}], [{"node": "错误响应", "type": "main", "index": 0}]]}, "解码图片": {"main": [[{"node": "Gemini智能分析", "type": "main", "index": 0}]]}, "Gemini智能分析": {"main": [[{"node": "格式化结果", "type": "main", "index": 0}], [{"node": "分析错误响应", "type": "main", "index": 0}]]}, "格式化结果": {"main": [[{"node": "成功响应", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}